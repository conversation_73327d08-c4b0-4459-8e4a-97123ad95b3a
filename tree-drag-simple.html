<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>船舶分段管理系统</title>
    <!-- Layui CSS -->
    <link rel="stylesheet" href="layui/css/layui.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            padding: 10px;
        }

        .container {
            display: flex;
            height: calc(100vh - 120px);
            gap: 10px;
        }

        .panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            overflow: auto;
        }

        .left-panel {
            flex: 0 0 70%;
        }

        .right-panel {
            flex: 0 0 30%;
        }

        .panel-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #1890ff;
        }

        .stats-info {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 14px;
            color: #0369a1;
        }

        /* 树结构样式 */
        /* 思维脑图样式的树形结构 */
        .tree-container {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.5;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        /* 一级节点 - 中心节点 */
        .tree-node-level1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 30px;
            position: relative;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            z-index: 10;
        }



        /* 二级节点容器 */
        .level2-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            width: 100%;
            position: relative;
        }

        /* 二级节点 */
        .tree-node-level2 {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            padding: 12px 18px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
            text-align: center;
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .tree-node-level2:hover {
            background: #e3f2fd;
            border-color: #1890ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
        }

        .tree-node-level2.drop-target {
            background: #e8f5e8 !important;
            border-color: #52c41a !important;
            border-width: 3px !important;
            border-style: dashed !important;
            transform: scale(1.05);
        }

        /* 二级节点连接线到中心 */
        .tree-node-level2::before {
            content: "";
            position: absolute;
            width: 2px;
            height: 30px;
            background: linear-gradient(to bottom, #667eea, #e9ecef);
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
        }



        /* 三级节点容器 */
        .level3-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-top: 15px;
            position: relative;
            padding-left: 20px;
        }

        /* 三级节点 */
        .tree-node-level3 {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s ease;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            min-width: 150px;
        }

        .tree-node-level3:hover {
            background: #fff1b8;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        /* 三级节点连接线到二级节点 */
        .tree-node-level3::before {
            content: "";
            position: absolute;
            width: 15px;
            height: 2px;
            background: #ffeaa7;
            left: -15px;
            top: 50%;
            transform: translateY(-50%);
        }

        /* 二级节点到三级节点的垂直连接线 */
        .level3-container::before {
            content: "";
            position: absolute;
            width: 2px;
            height: 15px;
            background: #e9ecef;
            left: -17px;
            top: -15px;
        }

        /* 三级节点的垂直连接线 */
        .level3-container::after {
            content: "";
            position: absolute;
            width: 2px;
            background: #ffeaa7;
            left: -17px;
            top: 0;
            bottom: 0;
        }

        /* 最后一个三级节点的连接线处理 */
        .tree-node-level3:last-child::after {
            /* content: "";
            position: absolute;
            width: 2px;
            height: 50%;
            background: white;
            left: -17px;
            bottom: 0; */
        }

        /* 展开/收起按钮 */
        .expand-toggle {
            position: absolute;
            left: -30px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 1px solid #d9d9d9;
            background: white;
            border-radius: 2px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #666;
            transition: all 0.2s ease;
        }

        .expand-toggle:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .expand-toggle.collapsed::after {
            content: "+";
        }

        .expand-toggle.expanded::after {
            content: "−";
        }

        /* 收起状态 */
        .tree-node-level2.collapsed .level3-container {
            display: none;
        }

        .tree-node-level2.collapsed::before {
            height: 50%;
        }

        /* 空的PE段样式 */
        .tree-node-level2.empty {
            opacity: 0.6;
            border-style: dashed;
        }

        .tree-node-level2.empty .item-info::after {
            content: " (空)";
            color: #999;
            font-size: 12px;
        }

        .tree-node-level3:hover .segment-actions {
            display: flex;
        }

        .segment-actions {
            display: none;
            gap: 5px;
            flex-shrink: 0;
        }

        .tree-node-level3 .item-info {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .action-btn {
            width: 22px;
            height: 22px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            padding: 0;
        }

        .action-btn i {
            font-size: 12px;
            pointer-events: none;
            /* 防止图标阻止按钮点击事件 */
        }

        .edit-btn {
            background: #1890ff;
            color: white;
        }

        .edit-btn:hover {
            background: #40a9ff;
        }

        .delete-btn {
            background: #ff4d4f;
            color: white;
        }

        .delete-btn:hover {
            background: #ff7875;
        }

        .segment-name-input {
            border: 1px solid #1890ff;
            border-radius: 3px;
            padding: 2px 5px;
            font-size: 14px;
            background: white;
            outline: none;
        }

        /* 右侧列表样式 */
        .draggable-item {
            background: white;
            border: 1px solid #e6e6e6;
            padding: 12px 15px;
            margin: 8px 0;
            border-radius: 6px;
            cursor: move;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .draggable-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .draggable-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .draggable-item.selected {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border-color: #0984e3;
        }

        .item-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .item-name {
            font-weight: bold;
        }

        .item-code {
            font-size: 12px;
            background: rgba(255, 255, 255, 0.7);
            padding: 2px 6px;
            border-radius: 3px;
            margin-right: 15px;
            display: none
        }

        .item-checkbox {
            position: absolute;
        }

        .selected .item-code {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .selected .item-details {
            color: white !important;
        }

        .selected .item-details div {
            color: white !important;
        }

        /* 搜索和操作区域 */
        .search-area {
            margin-bottom: 15px;
        }

        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .btn-group {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #f0f0f0;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .checkbox {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 16px;
            height: 16px;
        }

        /* 操作按钮 */
        .action-buttons {
            margin-top: 20px;
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .action-buttons .btn {
            margin: 0 5px;
            padding: 10px 20px;
            font-size: 14px;
        }

        .hidden {
            display: none;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 左侧树结构面板 -->
        <div class="panel left-panel">
            <div class="panel-title">船舶分段结构</div>
            <div class="stats-info">
                <div>总计: <span id="total-count">0</span> 个分段</div>
                <div>已分配: <span id="assigned-count">0</span> 个 | 未分配: <span id="unassigned-count">0</span> 个</div>
            </div>
            <div id="tree-container">
                <!-- 树结构将在这里动态生成 -->
            </div>
        </div>

        <!-- 右侧待分配列表面板 -->
        <div class="panel right-panel">
            <div class="panel-title">待分配的分段</div>
            <div class="stats-info">
                <div>待分配数量: <span id="pending-count">0</span> 个</div>
                <div>拖拽到左侧对应的PE段进行分配</div>
            </div>

            <!-- 搜索和批量操作 -->
            <div class="search-area">
                <input type="text" id="search-input" placeholder="搜索分段名称或编码..." class="search-input">
                <div class="btn-group">
                    <button class="btn" id="select-all">全选</button>
                    <button class="btn" id="select-none">取消全选</button>
                    <button class="btn btn-primary" id="batch-assign" disabled>批量分配</button>
                    <button class="btn" id="clear-search">清除搜索</button>
                </div>
            </div>

            <div id="items-list">
                <!-- 待分配的三级数据将在这里显示 -->
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
        <button class="btn" id="reset-btn">重置</button>
        <button class="btn" id="preview-btn">预览</button>
        <button class="btn btn-primary" id="save-btn"> 保存配置</button>
    </div>

    <!-- Layui JS -->
    <script src="layui/layui.js"></script>
    <script>
        // 初始化layui
        layui.use(['layer'], function () {
            const layer = layui.layer;
            window.layer = layer; // 全局可用
        });

        // 模拟数据
        const mockData = {
            level1: {
                id: 'root',
                name: 'N1193',
                children: []
            },
            level2: [],
            level3: []
        };

        // 生成模拟的PE段数据
        const peSegments = ['B50', 'B51', 'B52', '1舱口', '2舱口', '3舱口', 'C10', 'C11', 'C12', 'D20', 'D21', 'D22', 'E30', 'E31', 'E32', 'F40', 'F41', 'F42', 'G50', 'G51'];
        for (let i = 0; i < peSegments.length; i++) {
            mockData.level2.push({
                id: `pe_${i + 1}`,
                name: peSegments[i],
                code: `PE${String(i + 1).padStart(3, '0')}`,
                parentId: 'root',
                children: []
            });
        }

        // 生成模拟的分段数据
        for (let i = 1; i <= 50; i++) {
            mockData.level3.push({
                id: `segment_${i}`,
                name: `分段${String(i).padStart(3, '0')}`,
                code: `SEG${String(i).padStart(4, '0')}`,
                length: (Math.random() * 10 + 5).toFixed(2),
                width: (Math.random() * 5 + 2).toFixed(2),
                height: (Math.random() * 3 + 1).toFixed(2),
                description: `分段${String(i).padStart(3, '0')}的详细信息`,
                parentId: null
            });
        }

        let currentTreeData = JSON.parse(JSON.stringify(mockData));

        // 初始化页面
        function initPage() {
            renderTree();
            renderPendingItems();
            updateStats();
            initDragAndDrop();
            bindEvents();
        }

        // 渲染树结构
        function renderTree() {
            const container = document.getElementById('tree-container');
            container.innerHTML = '';

            // 渲染一级节点
            const level1Html = `
                <div class="tree-node-level1">
                    ${currentTreeData.level1.name}
                </div>
            `;

            // 创建二级节点容器
            const level2ContainerHtml = `
                <div class="level2-container">
                    ${currentTreeData.level2.map(item => {
                const level3Items = currentTreeData.level3.filter(l3 => l3.parentId === item.id);
                const hasItems = level3Items.length > 0;
                const emptyClass = hasItems ? '' : ' empty';

                return `
                            <div class="tree-node-level2${emptyClass}" data-id="${item.id}" data-level="2">
                                ${hasItems ? `<div class="expand-toggle expanded" data-target="${item.id}"></div>` : ''}
                                <div class="item-info">
                                    <div class="item-name">${item.name}</div>
                                    <div class="item-code" style="font-size: 12px; color: #666;">${item.code}</div>
                                    <div style="color: #999; font-size: 11px; margin-top: 5px;">${level3Items.length}个分段</div>
                                </div>
                                <div class="level3-container" data-parent="${item.id}">
                                    ${level3Items.map(l3Item => `
                                        <div class="tree-node-level3" data-id="${l3Item.id}" draggable="true">
                                            <div class="item-info">
                                                <span class="item-name segment-name" data-original="${l3Item.name}">${l3Item.name}</span>
                                                <span class="item-code">${l3Item.code}</span>
                                            </div>
                                            <div class="segment-actions">
                                                <button class="action-btn edit-btn" title="编辑名称">
                                                    <i class="layui-icon layui-icon-edit"></i>
                                                </button>
                                                <button class="action-btn delete-btn" title="删除分段">
                                                    <i class="layui-icon layui-icon-close"></i>
                                                </button>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `;
            }).join('')}
                </div>
            `;

            container.innerHTML = level1Html + level2ContainerHtml;
        }

        // 渲染待分配项目
        function renderPendingItems(searchTerm = '') {
            const container = document.getElementById('items-list');
            container.innerHTML = '';

            const pendingItems = currentTreeData.level3.filter(item => !item.parentId);

            pendingItems.forEach(item => {
                const matchesSearch = !searchTerm ||
                    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    item.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    item.description.toLowerCase().includes(searchTerm.toLowerCase());

                if (matchesSearch) {
                    const itemHtml = `
                        <div class="draggable-item" data-id="${item.id}" draggable="true">
                            <input type="checkbox" class="checkbox item-checkbox" data-id="${item.id}">
                            <div class="item-info">
                                <span class="item-name">${item.name}</span>
                                <span class="item-code">${item.code}</span>
                            </div>
                            <div class="item-details" style="font-size: 12px; color: #666; margin-top: 5px; padding-right: 30px;">
                                <div>尺寸: 长${item.length}m × 宽${item.width}m × 高${item.height}m</div>
                                <div style="margin-top: 2px;">${item.description}</div>
                            </div>
                        </div>
                    `;
                    container.innerHTML += itemHtml;
                }
            });

            updateBatchAssignButton();
        }

        // 更新统计信息
        function updateStats() {
            const totalCount = currentTreeData.level3.length;
            const assignedCount = currentTreeData.level3.filter(item => item.parentId).length;
            const unassignedCount = totalCount - assignedCount;

            document.getElementById('total-count').textContent = totalCount;
            document.getElementById('assigned-count').textContent = assignedCount;
            document.getElementById('unassigned-count').textContent = unassignedCount;
            document.getElementById('pending-count').textContent = unassignedCount;
        }

        // 更新批量分配按钮状态
        function updateBatchAssignButton() {
            const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
            const batchBtn = document.getElementById('batch-assign');

            if (checkedCount > 0) {
                batchBtn.disabled = false;
                batchBtn.textContent = `批量分配 (${checkedCount})`;
            } else {
                batchBtn.disabled = true;
                batchBtn.textContent = '批量分配';
            }
        }

        // 初始化拖拽功能
        function initDragAndDrop() {
            const draggableItems = document.querySelectorAll('.draggable-item, .tree-node-level3[draggable="true"]');
            const dropTargets = document.querySelectorAll('.tree-node-level2');

            // 为拖拽项目添加事件（包括右侧待分配和左侧已分配的分段）
            draggableItems.forEach(item => {
                item.addEventListener('dragstart', function (e) {
                    e.dataTransfer.setData('text/plain', this.dataset.id);
                    e.dataTransfer.setData('source', this.classList.contains('draggable-item') ? 'pending' : 'assigned');
                    this.classList.add('dragging');
                });

                item.addEventListener('dragend', function (e) {
                    this.classList.remove('dragging');
                });
            });

            // 为放置目标添加事件
            dropTargets.forEach(target => {
                target.addEventListener('dragover', function (e) {
                    e.preventDefault();
                    this.classList.add('drop-target');
                });

                target.addEventListener('dragleave', function (e) {
                    this.classList.remove('drop-target');
                });

                target.addEventListener('drop', function (e) {
                    e.preventDefault();
                    this.classList.remove('drop-target');

                    const droppedItemId = e.dataTransfer.getData('text/plain');
                    const source = e.dataTransfer.getData('source');
                    const targetLevel2Id = this.dataset.id;

                    // 更新数据
                    const item = currentTreeData.level3.find(item => item.id === droppedItemId);
                    if (item) {
                        const oldParentId = item.parentId;
                        item.parentId = targetLevel2Id;

                        // 重新渲染
                        renderTree();
                        renderPendingItems();
                        updateStats();
                        initDragAndDrop();

                        // 显示成功消息
                        if (window.layer) {
                            const targetName = this.querySelector('.item-name').textContent;
                            if (source === 'assigned' && oldParentId) {
                                layer.msg(`已将 "${item.name}" 移动到 "${targetName}"`, { icon: 1 });
                            } else {
                                layer.msg(`已将 "${item.name}" 分配到 "${targetName}"`, { icon: 1 });
                            }
                        }
                    }
                });
            });
        }

        // 绑定事件
        function bindEvents() {
            // 搜索功能
            document.getElementById('search-input').addEventListener('input', function () {
                const searchTerm = this.value.trim();
                renderPendingItems(searchTerm);
                initDragAndDrop();
            });

            // 清除搜索
            document.getElementById('clear-search').addEventListener('click', function () {
                document.getElementById('search-input').value = '';
                renderPendingItems();
                initDragAndDrop();
            });

            // 全选
            document.getElementById('select-all').addEventListener('click', function () {
                const checkboxes = document.querySelectorAll('.item-checkbox');
                checkboxes.forEach(cb => {
                    cb.checked = true;
                    cb.closest('.draggable-item').classList.add('selected');
                });
                updateBatchAssignButton();
            });

            // 取消全选
            document.getElementById('select-none').addEventListener('click', function () {
                const checkboxes = document.querySelectorAll('.item-checkbox');
                checkboxes.forEach(cb => {
                    cb.checked = false;
                    cb.closest('.draggable-item').classList.remove('selected');
                });
                updateBatchAssignButton();
            });

            // 批量分配
            document.getElementById('batch-assign').addEventListener('click', function () {
                showBatchAssignDialog();
            });

            // 复选框变化事件
            document.addEventListener('change', function (e) {
                if (e.target.classList.contains('item-checkbox')) {
                    const item = e.target.closest('.draggable-item');
                    if (e.target.checked) {
                        item.classList.add('selected');
                    } else {
                        item.classList.remove('selected');
                    }
                    updateBatchAssignButton();
                }
            });

            // 点击项目选中/取消选中
            document.addEventListener('click', function (e) {
                if (e.target.closest('.draggable-item') && !e.target.classList.contains('item-checkbox')) {
                    const item = e.target.closest('.draggable-item');
                    const checkbox = item.querySelector('.item-checkbox');
                    checkbox.checked = !checkbox.checked;
                    checkbox.dispatchEvent(new Event('change'));
                }
            });

            // 重置按钮
            document.getElementById('reset-btn').addEventListener('click', function () {
                if (window.layer) {
                    layer.confirm('确定要重置所有分配吗？', {
                        icon: 3,
                        title: '确认重置'
                    }, function (index) {
                        currentTreeData.level3.forEach(item => {
                            item.parentId = null;
                        });

                        renderTree();
                        renderPendingItems();
                        updateStats();
                        initDragAndDrop();

                        layer.close(index);
                        layer.msg('重置成功！', { icon: 1 });
                    });
                } else {
                    if (confirm('确定要重置所有分配吗？')) {
                        currentTreeData.level3.forEach(item => {
                            item.parentId = null;
                        });

                        renderTree();
                        renderPendingItems();
                        updateStats();
                        initDragAndDrop();

                        alert('重置成功！');
                    }
                }
            });

            // 预览结构按钮
            document.getElementById('preview-btn').addEventListener('click', function () {
                showPreview();
            });

            // 保存配置按钮
            document.getElementById('save-btn').addEventListener('click', function () {
                saveConfiguration();
            });

            // 编辑分段名称
            document.addEventListener('click', function (e) {
                // 检查点击的是编辑按钮或其内部的图标
                const editBtn = e.target.closest('.edit-btn');
                if (editBtn) {
                    const segmentNode = editBtn.closest('.tree-node-level3');
                    const nameSpan = segmentNode.querySelector('.segment-name');
                    const currentName = nameSpan.textContent;
                    const segmentId = segmentNode.dataset.id;

                    // 创建输入框
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.value = currentName;
                    input.className = 'segment-name-input';

                    // 替换文本为输入框
                    nameSpan.style.display = 'none';
                    nameSpan.parentNode.insertBefore(input, nameSpan);
                    input.focus();
                    input.select();

                    // 保存编辑
                    function saveEdit() {
                        const newName = input.value.trim();
                        if (newName && newName !== currentName) {
                            // 更新数据
                            const item = currentTreeData.level3.find(item => item.id === segmentId);
                            if (item) {
                                item.name = newName;
                                nameSpan.textContent = newName;
                                nameSpan.dataset.original = newName;

                                if (window.layer) {
                                    layer.msg('分段名称修改成功！', { icon: 1 });
                                }
                            }
                        }

                        // 恢复显示
                        input.remove();
                        nameSpan.style.display = '';
                    }

                    // 取消编辑
                    function cancelEdit() {
                        input.remove();
                        nameSpan.style.display = '';
                    }

                    // 绑定事件
                    input.addEventListener('blur', saveEdit);
                    input.addEventListener('keydown', function (e) {
                        if (e.key === 'Enter') {
                            saveEdit();
                        } else if (e.key === 'Escape') {
                            cancelEdit();
                        }
                    });
                }
            });

            // 删除分段
            document.addEventListener('click', function (e) {
                // 检查点击的是删除按钮或其内部的图标
                const deleteBtn = e.target.closest('.delete-btn');
                if (deleteBtn) {
                    const segmentNode = deleteBtn.closest('.tree-node-level3');
                    const segmentId = segmentNode.dataset.id;
                    const item = currentTreeData.level3.find(item => item.id === segmentId);

                    if (item) {
                        if (window.layer) {
                            layer.confirm(`确定要删除分段 "${item.name}" 吗？`, {
                                icon: 3,
                                title: '确认删除'
                            }, function (index) {
                                // 将分段移回待分配列表
                                item.parentId = null;

                                // 重新渲染
                                renderTree();
                                renderPendingItems();
                                updateStats();
                                initDragAndDrop();

                                layer.close(index);
                                layer.msg(`分段 "${item.name}" 已移回待分配列表`, { icon: 1 });
                            });
                        } else {
                            if (confirm(`确定要删除分段 "${item.name}" 吗？`)) {
                                // 将分段移回待分配列表
                                item.parentId = null;

                                // 重新渲染
                                renderTree();
                                renderPendingItems();
                                updateStats();
                                initDragAndDrop();

                                alert(`分段 "${item.name}" 已移回待分配列表`);
                            }
                        }
                    }
                }
            });

            // 展开/收起功能
            document.addEventListener('click', function (e) {
                if (e.target.classList.contains('expand-toggle')) {
                    const toggle = e.target;
                    const level2Node = toggle.closest('.tree-node-level2');

                    if (toggle.classList.contains('expanded')) {
                        // 收起
                        toggle.classList.remove('expanded');
                        toggle.classList.add('collapsed');
                        level2Node.classList.add('collapsed');
                    } else {
                        // 展开
                        toggle.classList.remove('collapsed');
                        toggle.classList.add('expanded');
                        level2Node.classList.remove('collapsed');
                    }
                }
            });
        }

        // 显示批量分配对话框
        function showBatchAssignDialog() {
            const checkedItems = document.querySelectorAll('.item-checkbox:checked');
            if (checkedItems.length === 0) {
                if (window.layer) {
                    layer.msg('请先选择要分配的分段！', { icon: 2 });
                } else {
                    alert('请先选择要分配的分段！');
                }
                return;
            }

            // 创建对话框
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                padding: 20px;
                border-radius: 8px;
                width: 500px;
                max-height: 80vh;
                overflow-y: auto;
            `;

            // 生成二级节点选择列表
            let optionsHtml = '<option value="">请选择目标PE段</option>';
            currentTreeData.level2.forEach(item => {
                optionsHtml += `<option value="${item.id}">${item.name} (${item.code})</option>`;
            });

            content.innerHTML = `
                <h3>批量分配分段</h3>
                <div style="margin: 15px 0;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">选择目标PE段：</label>
                    <select id="target-level2" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        ${optionsHtml}
                    </select>
                </div>
                <div style="margin: 15px 0;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">将要分配的分段 (${checkedItems.length} 个)：</label>
                    <div style="max-height: 200px; overflow-y: auto; border: 1px solid #e6e6e6; padding: 10px; border-radius: 4px; background: #f9f9f9;">
                        ${Array.from(checkedItems).map(cb => {
                const itemId = cb.dataset.id;
                const item = currentTreeData.level3.find(i => i.id === itemId);
                return `<div style="margin: 5px 0; padding: 5px; background: white; border-radius: 3px;">• ${item.name} (${item.code})</div>`;
            }).join('')}
                    </div>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button id="confirm-assign" class="btn btn-primary" style="margin-right: 10px;">确认分配</button>
                    <button id="cancel-assign" class="btn">取消</button>
                </div>
            `;

            dialog.appendChild(content);
            document.body.appendChild(dialog);

            // 绑定事件
            document.getElementById('confirm-assign').addEventListener('click', function () {
                const targetId = document.getElementById('target-level2').value;
                if (!targetId) {
                    if (window.layer) {
                        layer.msg('请选择目标PE段！', { icon: 2 });
                    } else {
                        alert('请选择目标PE段！');
                    }
                    return;
                }

                // 执行批量分配
                checkedItems.forEach(cb => {
                    const itemId = cb.dataset.id;
                    const item = currentTreeData.level3.find(i => i.id === itemId);
                    if (item) {
                        item.parentId = targetId;
                    }
                });

                // 重新渲染
                renderTree();
                renderPendingItems();
                updateStats();
                initDragAndDrop();

                document.body.removeChild(dialog);
                if (window.layer) {
                    layer.msg(`成功分配 ${checkedItems.length} 个分段！`, { icon: 1 });
                } else {
                    alert(`成功分配 ${checkedItems.length} 个分段！`);
                }
            });

            document.getElementById('cancel-assign').addEventListener('click', function () {
                document.body.removeChild(dialog);
            });

            // 点击背景关闭
            dialog.addEventListener('click', function (e) {
                if (e.target === dialog) {
                    document.body.removeChild(dialog);
                }
            });
        }

        // 显示预览
        function showPreview() {
            let previewContent = `<h3>${currentTreeData.level1.name}</h3>`;

            currentTreeData.level2.forEach(level2 => {
                const level3Items = currentTreeData.level3.filter(item => item.parentId === level2.id);
                previewContent += `
                    <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                        <strong>${level2.name} (${level2.code})</strong>
                        <span style="color: #666; margin-left: 10px;">[${level3Items.length} 个项目]</span>
                `;

                if (level3Items.length > 0) {
                    previewContent += '<ul style="margin: 5px 0 0 20px; padding: 0;">';
                    level3Items.forEach(item => {
                        previewContent += `<li style="list-style: none; margin: 3px 0; color: #333;">• ${item.name} (${item.code})</li>`;
                    });
                    previewContent += '</ul>';
                }
                previewContent += '</div>';
            });

            // 创建预览对话框
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                padding: 20px;
                border-radius: 8px;
                width: 600px;
                max-height: 80vh;
                overflow-y: auto;
            `;

            content.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>树结构预览</h3>
                    <button id="close-preview" class="btn">关闭</button>
                </div>
                <div style="max-height: 400px; overflow-y: auto;">
                    ${previewContent}
                </div>
            `;

            dialog.appendChild(content);
            document.body.appendChild(dialog);

            // 绑定关闭事件
            document.getElementById('close-preview').addEventListener('click', function () {
                document.body.removeChild(dialog);
            });

            dialog.addEventListener('click', function (e) {
                if (e.target === dialog) {
                    document.body.removeChild(dialog);
                }
            });
        }

        // 保存配置
        function saveConfiguration() {
            const assignedCount = currentTreeData.level3.filter(item => item.parentId).length;
            const totalCount = currentTreeData.level3.length;

            if (assignedCount === 0) {
                if (window.layer) {
                    layer.msg('请至少分配一个分段！', { icon: 2 });
                } else {
                    alert('请至少分配一个分段！');
                }
                return;
            }

            if (window.layer) {
                layer.confirm(`确定保存当前配置吗？<br>已分配: ${assignedCount}/${totalCount} 个分段`, {
                    icon: 3,
                    title: '确认保存'
                }, function (index) {
                    // 构建树形结构数据
                    const saveData = buildTreeStructure();

                    // 这里可以发送数据到后端
                    console.log('保存的树形结构数据:', saveData);
                    layer.close(index);
                    layer.msg('保存成功！', { icon: 1 });

                    // 可以在这里添加实际的保存逻辑，比如发送到服务器
                    // fetch('/api/save-tree-structure', {
                    //     method: 'POST',
                    //     headers: { 'Content-Type': 'application/json' },
                    //     body: JSON.stringify(saveData)
                    // });
                });
            } else {
                if (confirm(`确定保存当前配置吗？\n已分配: ${assignedCount}/${totalCount} 个分段`)) {
                    // 构建树形结构数据
                    const saveData = buildTreeStructure();

                    // 这里可以发送数据到后端
                    console.log('保存的树形结构数据:', saveData);
                    alert('保存成功！');

                    // 可以在这里添加实际的保存逻辑，比如发送到服务器
                    // fetch('/api/save-tree-structure', {
                    //     method: 'POST',
                    //     headers: { 'Content-Type': 'application/json' },
                    //     body: JSON.stringify(saveData)
                    // });
                }
            }
        }

        // 构建树形结构数据
        function buildTreeStructure() {
            // 深拷贝一级节点
            const treeData = {
                id: currentTreeData.level1.id,
                name: currentTreeData.level1.name,
                type: 'ship',
                children: [],
                timestamp: new Date().toISOString()
            };

            // 构建二级节点（PE段）
            currentTreeData.level2.forEach(level2Item => {
                const level2Node = {
                    id: level2Item.id,
                    name: level2Item.name,
                    code: level2Item.code,
                    type: 'pe_segment',
                    children: []
                };

                // 查找该PE段下的所有分段
                const level3Items = currentTreeData.level3.filter(l3 => l3.parentId === level2Item.id);

                // 构建三级节点（分段）
                level3Items.forEach(level3Item => {
                    const level3Node = {
                        id: level3Item.id,
                        name: level3Item.name,
                        code: level3Item.code,
                        type: 'segment',
                        dimensions: {
                            length: parseFloat(level3Item.length),
                            width: parseFloat(level3Item.width),
                            height: parseFloat(level3Item.height)
                        },
                        description: level3Item.description
                    };
                    level2Node.children.push(level3Node);
                });

                treeData.children.push(level2Node);
            });

            // 添加统计信息
            treeData.statistics = {
                totalPeSegments: currentTreeData.level2.length,
                totalSegments: currentTreeData.level3.length,
                assignedSegments: currentTreeData.level3.filter(item => item.parentId).length,
                unassignedSegments: currentTreeData.level3.filter(item => !item.parentId).length
            };

            return treeData;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPage);
    </script>
</body>

</html>