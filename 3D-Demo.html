<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <th:block th:include="include::header('搭载')" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" th:href="@{/febs/views/css/screen/commonScreen.css}" media="all">
    <link rel="stylesheet" th:href="@{/plugins/animsition/css/animsition.min.css}" media="all">
    <link rel="stylesheet" th:href="@{/febs/views/css/screen/shipModel.css}" media="all">
    <link rel="stylesheet" th:href="@{/febs/views/css/screen/steps.css}" media="all">
    <link rel="stylesheet" th:href="@{/plugins/otherLinks/otherLinks.css}" media="all">
    <style type="text/css">
        .layui-layer-title {
            display: none;
        }

        .layui-layer {
            background-color: #061729;
        }

        /* 滚动条轨道 */
        ::-webkit-scrollbar-track {
            background: #49b1f5;
        }

        ::-webkit-scrollbar {
            display: block !important;
        }

        ::-webkit-scrollbar-thumb {
            background-color: #3c648b !important;
        }

        ::-webkit-scrollbar-track {
            background-color: #07325b;
        }

        #stepContent {
            scroll-behavior: smooth;
        }

        /* 手动排序相关样式 */
        .manual-sort-mode {
            position: relative;
        }

        .sort-module {
            position: absolute;
            width: 74px;
            height: 47px;
            background: rgba(52, 175, 210, 0.8);
            border: 2px solid #34afd2;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 11px;
            text-align: center;
            transition: all 0.3s ease;
            z-index: 1000;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .sort-module:hover {
            background: rgba(255, 114, 43, 0.9);
            border-color: #FF722B;
            transform: scale(1.05);
        }

        .sort-module.selected {
            background: rgba(255, 114, 43, 0.9);
            border-color: #FF722B;
            box-shadow: 0 6px 12px rgba(255, 114, 43, 0.5);
        }

        .sort-module-name {
            font-weight: bold;
            margin-bottom: 4px;
            font-size: 12px;
        }

        .sort-module-date {
            font-size: 12px;
            opacity: 0.8;
        }

        .sort-controls {
            display: none !important;
        }

        .sort-info {
            margin-bottom: 10px;
            font-size: 14px;
        }

        .sort-buttons {
            display: flex;
            gap: 10px;
        }

        .sort-btn {
            padding: 8px 16px;
            background: #3c648b;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .sort-btn:hover {
            background: #49b1f5;
        }

        .sort-btn.primary {
            background: #49b1f5;
        }

        .sort-btn.primary:hover {
            background: #34afd2;
        }

        .sort-order-display {
            position: fixed;
            top: 312px;
            left: 436px;
            background: rgba(6, 23, 41, 0.95);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #34afd2;
            color: white;
            max-width: 300px;
            min-width: 180px;
            z-index: 2000;
            display: none;
        }

        .sort-order-display.active {
            display: block;
        }

        .sort-order-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #49b1f5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .reset-sort-btn {
            padding: 4px 8px;
            background: #3c648b;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: background 0.3s ease;
        }

        .reset-sort-btn:hover {
            background: #34afd2;
        }

        .sort-order-list {
            max-height: 482px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .sort-order-item {
            padding: 8px 10px;
            border-bottom: 1px solid rgba(52, 175, 210, 0.3);
            font-size: 12px;
            cursor: move;
            background: rgba(255, 255, 255, 0.05);
            margin: 2px 0;
            border-radius: 4px;
            transition: all 0.3s ease;
            user-select: none;
            position: relative;
        }

        .sort-order-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(2px);
        }

        .sort-order-item.dragging {
            opacity: 0.5;
            transform: rotate(2deg);
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .sort-order-item.drag-over {
            border-top: 2px solid #34afd2;
            background: rgba(52, 175, 210, 0.2);
        }

        .sort-order-item .item-content {
            margin-left: 0px;
            font-size: 13px;
        }

        .sort-order-item:last-child {
            border-bottom: none;
        }
    </style>

</head>

<body style="overflow: hidden">
    <div id="shipModel" class="groupBox animsition" data-animsition-in-duration="1200">

        <div class="mainContent">
            <div class="menuLine">
                <img id="returnBack" th:src="@{/febs/images/virual/backBtn.png}" alt="" class="backBtn"
                    style="position: absolute;right: 40px;top: 12px;cursor: pointer;z-index: 9999;">
                <div>
                    <div class="shipNo" style="left: 9%"></div>
                    <div class="shipInfo">
                        <div class="shipInfoDetail">
                            <span style="color: darkgray">交船日期:</span>
                            <span class="dateVal"></span>
                        </div>
                        <div class="shipInfoDetail">
                            <span style="color: darkgray">船型:</span>
                            <span class="typeVal"></span>
                        </div>
                        <div class="shipInfoDetail">
                            <span style="color: darkgray">船东:</span>
                            <span class="ownerVal"></span>
                        </div>
                    </div>
                </div>

                <div id="stepContent">
                </div>
                <div class="tools">
                    <div style="padding: 16px;" class="toolsDetail">
                        <div class="topBtn" id="splitpy" style="display: flex;">平移分解</div>
                        <div class="topBtn" id="splitbz" style="display: flex;">爆炸分解</div>
                        <div class="topBtn" id="dzmn" style="display: flex;">搭载模拟</div>
                        <!--                    <div class="topBtn" id="hidemodel" style="display: flex;">隐藏模型</div>-->
                        <div class="topBtn" id="merge" style="display: flex;">合并模型</div>
                        <div class="topBtn" id="showUp" style="display: flex;">旋转模型</div>
                        <div class="topBtn" id="manualSort" style="display: flex;">手动排序</div>
                        <div class="topBtn" id="recoverBtn" style="display: flex;">还原</div>
                        <!--                                <button class="topBtn" id="modelsChange" style="display: flex;">批量操作</button>-->
                    </div>
                </div>
                <div id="tipsBox" class="febs-hide">
                </div>
                <div id="blockDetail" class="febs-hide">
                </div>
            </div>
            <!-- <img class="changeRight" src="/visual/febs/images/virual/group/arrR.png" alt="">-->
            <div class="menubox">
                <select id="nameOption">
                    <option value=""></option>
                </select>
                <button id="search" style="display: flex;">查找</button>
            </div>
            <!-- <div id="boxMenu" class="boxmenu">
<ul id="menuList">
</ul>
</div> -->
            <div id="canvas"></div>

            <!-- 手动排序控制面板 -->
            <div id="sortControls" class="sort-controls">
                <div class="sort-info">手动排序模式 - 点击模块进行搭载</div>
                <div class="sort-buttons">
                    <button class="sort-btn primary" id="exitSortMode">退出排序</button>
                    <button class="sort-btn" id="resetSort">重置排序</button>
                </div>
            </div>

            <!-- 排序顺序显示 -->
            <div id="sortOrderDisplay" class="sort-order-display">
                <div class="sort-order-title">
                    <span>搭载顺序</span>
                    <button class="reset-sort-btn" id="resetSortBtn">重置当前排序</button>
                    <button class="reset-sort-btn" id="resetSortBtn2" style="margin-left: 5px">恢复默认排序</button>
                </div>
                <div class="sort-order-list" id="sortOrderList">
                    <!-- 动态生成排序列表 -->
                </div>
            </div>

        </div>
    </div>
    <th:block th:include="include::foot" />
    <script th:src="@{/plugins/animsition/js/animsition.min.js}"></script>
    <script th:src="@{/plugins/otherLinks/jquery-otherlinks.js}"></script>
    <script th:src="@{/plugins/tween.js-23.1.3/dist/tween.umd.js}"></script>
    <script th:src="@{/plugins/typed.umd.js}"></script>
    <script th:inline="javascript">
    </script>
    <script type="importmap" data-turbo-track="reload">

        {
            "imports": {
                "three": "[[@{/plugins/threejs/build/three.module.js}]]",
                "three/addons/": "[[@{/plugins/threejs/examples/jsm/}]]"
            }
        }

    </script>
    <script data-th-inline="javascript" type="text/javascript">
        $(document).ready(function () {

            $(".animsition").animsition({
                inClass: 'zoom-in',
                outClass: 'zoom-out',
                inDuration: 1200,
                outDuration: 1200,
                linkElement: '.animsition-link',
                // e.g. linkElement   :   'a:not([target="_blank"]):not([href^=#])'
                loading: true,
                loadingParentElement: 'body', //animsition wrapper element
                loadingClass: 'animsition-loading',
                unSupportCss: ['animation-duration',
                    '-webkit-animation-duration',
                    '-o-animation-duration'
                ],
                overlay: false,
                overlayClass: 'animsition-overlay-slide',
                overlayParentElement: 'body'
            });

            // 屏幕自适应
            function resize() {
                var ratioX = $(window).width() / 1920;
                var ratioY = $(window).height() / 1080;
                $("body").css({
                    transform: "scale(" + ratioX + "," + ratioY + ")",
                    transformOrigin: "left top",
                    backgroundSize: "100% 100%"
                });
                $("html").css({ 'overflow': 'hidden' })
            }

            $(window, document).resize(function () {
                resize();
            });
            resize();
            $("#returnBack").on('click', function () {//返回按钮
                ctx = ctx == "\/" ? "" : ctx;
                window.history.back()
            })
        })
        var ctx = [[@{/}]];
        ctx = ctx == "\/" ? "" : ctx;
        var currentUser = [[${ user }]];

        var shipId = [[${ shipId }]];
        var shipNo = [[${ shipNo }]];


    </script>
    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
        import { FBXLoader } from 'three/addons/loaders/FBXLoader.js';
        import { CSS2DRenderer, CSS2DObject } from 'three/addons/renderers/CSS2DRenderer.js';
        let camera, scene, renderer, controls, container, labelRenderer, light, modul, itemList = [], chooseModels = [];

        // 手动排序相关变量
        let isManualSortMode = false;
        let sortModules = [];
        let sortOrder = [];
        let originalModulePositions = new Map();
        let frameModel = null;
        let currentSelectedDate = null; // 保存当前选中的搭载日期

        var febs = null;

        layui.config({
            base: ctx + '/febs/',
            debug: true
        }).extend({
            febs: 'lay/modules/febs',
            request: 'lay/modules/request',
            formSelects: 'lay/extends/formSelects-v4.min',
            treeSelect: 'lay/extends/treeSelect',
            apexcharts: 'lay/extends/apexcharts.min',
            eleTree: 'lay/extends/eleTree',
            selectN: 'lay/extends/selectN',
            selectM: 'lay/extends/selectM',
            commonJS: 'lay/extends/common',
            jqueryui: 'lay/extends/jquery-ui.min',
            echarts: 'lay/extends/echarts.min',
            treetable: 'lay/extends/treetable',
            tagsinput: 'lay/extends/jquery.tagsinput',
            media: 'lay/extends/jquery.media',
            steps: 'lay/extends/steps',
        }).use(['febs', 'steps'], function () {
            var $ = layui.$,
                febs = layui.febs,
                $view = $("#shipModel"),
                steps = layui.steps;

            var stepData = [];
            let carryData = [];
            var modelURL = ''
            var modelScalar = undefined
            let isSimulating = false; //是否正在搭载
            let carryAll;
            function carryList() {

                febs.getSyn(ctx + "dockship/carryDateList", { shipId: shipId }, function (data) {
                    const stepContent = document.getElementById('stepContent');

                    let arr = data.data;
                    let index = 0;
                    let html = '';
                    let toJump = 0;
                    let date;
                    $.each(data.data, function (i, val) {
                        if (val.isCurrentOrNearest == 0 && i > 4) {
                            toJump = (i - 4) * 214;
                            date = val.carryDate
                        } else if (val.isCurrentOrNearest == 0) {
                            date = val.carryDate
                        }
                        html += `
                     <div class="nodeItem" data-carrydate="${val.carryDate}" style="display: flex;flex-direction: column;align-items: center" data-timeFlg="${val.timeFlg}" data-isCurrentOrNearest="${val.isCurrentOrNearest}" >
                        <div data-carrydate="${val.carryDate}" class="carryDate" style="${val.timeFlg === 0 || val.isCurrentOrNearest === 1 ? 'color: white' : 'color: darkgray'}">
                           ${val.carryDate}
                        </div>

                        <img class="noClick" src="${ctx}febs/images/virual/carry/${val.timeFlg === 0 ? 'checked.png' : 'noChecked.png'}">
                        <img  class="febs-hide click" src="${ctx}febs/images/virual/carry/${val.timeFlg === 0 ? 'checkedClick.png' : 'noCheckedClick.png'}">
                        <div  style="${val.timeFlg === 0 ? 'color: white' : 'color: darkgray'}">
                                ${val.completeCount}/${val.totalCount}
                        </div>
                     </div>
                    `;
                        if ((data.data.length - 1) != i) {
                            html += `
                            <img class="connectLine" src="${ctx}febs/images/virual/carry/connectLine.png">
                        `
                        }
                        stepData.push(val.carryDate)
                    })
                    $view.find("#stepContent").append(html);


                    // 确保 DOM 更新后触发
                    setTimeout(() => {
                        const $target = $view.find(".nodeItem[data-carrydate='" + date + "']");
                        if ($target.length) {
                            $target.trigger("click");
                            stepContent.scrollTo({
                                left: stepContent.scrollLeft + toJump,
                                behavior: 'smooth' // 启用平滑滚动
                            });
                        } else {
                        }
                    }, 100);
                })


            }





            febs.getSyn(ctx + "dockship/getShipDetail", { shipId: shipId }, function (data) {
                let ship = data.data;
                $view.find(".dateVal").text(ship.deliverDay)
                $view.find(".typeVal").text(ship.typeName)
                $view.find(".ownerVal").text(ship.shipOwner)
                $view.find(".shipNo").text(ship.shipNo)
            })

            $('#title1').html(shipNo)
            $('#tipsBox').hide()
            $("#blockDetail").hide()

            var oldintersects = [];


            let changeBox = document.createElement('div')
            changeBox.classList.add('changeBox')
            document.body.appendChild(changeBox);


            function init(date) {
                currentSelectedDate = date; // 保存当前选中的日期
                container = document.getElementById('canvas');
                camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.3, 12000);
                camera.position.set(0, 10, 50);
                scene = new THREE.Scene();
                // scene.background = new THREE.Color(0xFAD7A0);
                light = new THREE.AmbientLight(0xffffff, 1);
                // const axesHelper = new THREE.AxesHelper(50)
                scene.add(light);
                // scene.add(axesHelper)



                febs.getSyn(ctx + "dockship/carryDetail", { shipId: shipId, date: date }, function (data) {

                    modelURL = ctx + "/images/ship/categoryFile/" + data.data.catePic;
                    modelScalar = data.data.scalar;
                    carryAll = data.data.carryAll;
                    $.each(data.data.carryDetail, function (i, val) {
                        carryData.push({ peName: val.peName, carryDate: val.carryDate })
                        $('#nameOption').append(
                            `<option value="${val.peName}">${val.peName}</option>`
                        )
                    })
                    // initModel(carryData,modelScalar,modelURL)
                    carryList();
                })




                const raycaster = new THREE.Raycaster()
                const mouse = new THREE.Vector2()

                const onClick = (event) => {

                    // 如果正在搭载模拟或手动排序模式，则禁止点击
                    if (isSimulating || isManualSortMode) {
                        return;
                    }

                    // 获取canvas元素的位置和尺寸信息
                    const rect = canvas.getBoundingClientRect();

                    // 修正后的坐标转换：
                    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
                    mouse.y = 1 - ((event.clientY - rect.top) / rect.height) * 2;
                    raycaster.params.Mesh.threshold = 2; // 调整检测阈值，单位是世界坐标系中的距离
                    raycaster.setFromCamera(mouse, camera);


                    const intersects = raycaster.intersectObjects(scene.children, true)

                    if (intersects.length > 0) {
                        const obj = intersects[0].object;
                        obj.raycastEnabled = true;
                        if (!window.event.ctrlKey) {
                            del(obj.name)
                        } else {
                            let selectNames = []
                            for (let i = 0; i < chooseModels.length; i++) {
                                selectNames.push(chooseModels[i].name)
                            }
                            if (chooseModels.length > 0) {
                                if (selectNames.indexOf(obj.name) > -1) {
                                    obj.material.color = new THREE.Color(0x34afd2);
                                    chooseModels = chooseModels.filter(item => item.name != obj.name);
                                } else {
                                    chooseModels.push(obj)
                                    obj.material.color = new THREE.Color(0xffd9b5);
                                }
                            } else {
                                chooseModels.push(obj)
                                obj.material.color = new THREE.Color(0xffd9b5);
                            }
                            $('#modelsChange').click()


                            // selectModels(chooseModels)
                        }

                    } else {
                        if (oldintersects.length > 0) {
                            if (oldintersects[oldintersects.length - 1].material.color.b == 1) {
                                oldintersects[oldintersects.length - 1].material.color = new THREE.Color(0x34afd2);
                            }
                        }
                    }

                }


                renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
                renderer.setPixelRatio(window.devicePixelRatio);
                renderer.setSize(window.innerWidth * 0.8, window.innerHeight * 0.75);

                renderer.domElement.addEventListener('click', onClick)

                renderer.shadowMap.enabled = true;
                container.appendChild(renderer.domElement);
                controls = new OrbitControls(camera, renderer.domElement);
                controls.target.set(0, 0, 0);

                controls.autoRotate = false;
                controls.autoRotateSpeed = 1
                // controls.update();
                const texLoader = new THREE.TextureLoader();
                // const tex = texLoader.load('images/label-bg.png')





                labelRenderer = new CSS2DRenderer();
                labelRenderer.setSize(window.innerWidth, window.innerHeight);
                labelRenderer.domElement.style.position = 'absolute';
                labelRenderer.domElement.style.top = '0px';
                labelRenderer.domElement.style.left = '0px';

                //设置.pointerEvents=none，以免模型标签HTML元素遮挡鼠标选择场景模型
                labelRenderer.domElement.style.pointerEvents = 'none';
                container.appendChild(labelRenderer.domElement)



                window.addEventListener('resize', onWindowResize, false);

                animate();
            }


            function initModel(carryData, modelScalar, modelURL) {
                resetModel()
                itemList = [];
                let carryPeName = carryData.map(item => item.peName);
                var loader = new FBXLoader();
                loader.load(modelURL, function (object) {
                    $('li').click(function () {
                        const name = $(this).attr('data-name')
                        del(name)
                    })
                    object.position.set(0, 0, 0)

                    object.scale.multiplyScalar(modelScalar);
                    object.traverse((item) => {
                        if (item.isMesh && !item.name.includes("Hatch_cover")) {
                            const carryDate = carryAll.find(x => x.peName == item.name)?.carryDate || '';
                            itemList.push({ _e: item, name: item.name, carryDate: carryDate, itemX: item.position.x, itemY: item.position.y, itemZ: item.position.z })
                            if (carryPeName.length == 0 || carryPeName.includes(item.name)) {
                                const edges = new THREE.EdgesGeometry(item.geometry)
                                const em = new THREE.LineBasicMaterial({
                                    opacity: 0.6,
                                    color: 0xf0fbff,
                                    visible: true,
                                    linewidth: 0.2
                                })
                                const line = new THREE.LineSegments(edges, em)
                                item.add(line);
                                let newMaterial = new THREE.MeshLambertMaterial({
                                    color: new THREE.Color(0x34afd2), //可修改报警时的闪烁颜色
                                    transparent: true,
                                    opacity: 0.6, //可修改报警闪烁是的透明度
                                    wireframe: false,
                                    depthWrite: true,
                                    side: THREE.DoubleSide,
                                });
                                item.material = newMaterial
                            } else {
                                const edges = new THREE.EdgesGeometry(item.geometry)
                                const em = new THREE.LineBasicMaterial({
                                    opacity: 0.1,
                                    color: 0xc8d7e6,
                                    visible: true,
                                    linewidth: 0.2
                                })
                                const line = new THREE.LineSegments(edges, em)
                                item.add(line);
                                let newMaterial = new THREE.MeshLambertMaterial({
                                    color: new THREE.Color(0x8ba4bb), //可修改报警时的闪烁颜色
                                    transparent: true,
                                    opacity: 0.2, //可修改报警闪烁是的透明度
                                    wireframe: false,
                                    depthWrite: true,
                                    side: THREE.DoubleSide,
                                });
                                item.material = newMaterial
                            }
                        }
                    });
                    scene.add(object);
                    // object.rotation.y += 1.58
                    modul = object;
                    // 使用Tween.js创建动画
                    // initAssembly(); // 在场景初始化阶段调用
                });

            }



            // 改进的resetModel函数
            function resetModel() {
                // 清空 itemList 并解除引用
                itemList.forEach(item => {
                    if (item._e) {
                        item._e = null; // 显式解除引用
                    }
                });
                itemList.length = 0; // 快速清空数组

                // 移除场景中的旧模型
                if (modul) {
                    scene.remove(modul);
                    modul.traverse(child => {
                        if (child.isMesh) {
                            child.geometry?.dispose();
                            child.material?.dispose();
                            child.children?.forEach(c => {
                                if (c.isLineSegments) {
                                    c.geometry.dispose();
                                    c.material.dispose();
                                }
                            });
                        }
                    });
                    modul = null;
                }

            }


            function InitTextLabel(text, labelPoint, clickEvent) {
                //创建div容器
                this.el = document.createElement('div');
                this.el.className = 'label';
                this.el.textContent = text
                this.el.style.pointerEvents = 'auto'
                this.el.addEventListener('pointerdown', function (e) {
                    if (clickEvent)
                        clickEvent(e)
                })
                this.pointLabel = new CSS2DObject(this.el);
                this.pointLabel.position.copy(labelPoint);

            }

            function onWindowResize() {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            }
            //
            function animate() {
                requestAnimationFrame(animate);
                TWEEN.update()
                //stats.update();
                controls.update();
                renderer.render(scene, camera);
                labelRenderer.render(scene, camera)
            }
            //计算传入模型包围盒的中心点
            function getCenterPoint(obj) {
                //包围盒
                const box = new THREE.Box3().setFromObject(obj);
                var center = new THREE.Vector3();
                //获取包围盒的中心点
                box.getCenter(center);
                return center;
            }
            //爆炸拆解传入模型 type 1:平移，2：爆炸
            function explodeModel(obj, val, type) {
                isSimulating = false;
                if (!obj) return;
                const center = getCenterPoint(obj);
                let y = obj.position.y;
                obj.traverse((child) => {
                    if (child.isMesh) {
                        // 保存原始位置
                        if (!child.userData.originalPosition) {
                            child.userData.originalPosition = child.position.clone();
                        }
                        // 计算相对中心的位置
                        const originalPosition = child.userData.originalPosition;
                        const direction = originalPosition.clone().sub(center).normalize();
                        const distance = originalPosition.distanceTo(center); // 新增距离计算
                        let targetPosition;
                        if (type == 1) {
                            //等比例拆解
                            targetPosition = child.position.clone().multiplyScalar(1.5);
                        } else {
                            // // 计算目标位置，基于原始位置加上方向向量乘以拆解系数，中心点爆炸拆解
                            const explosionFactor = 1; // 爆炸系数
                            targetPosition = originalPosition
                                .clone()
                                .add(direction.multiplyScalar((val - 1) * explosionFactor));

                        }
                        // 创建动画
                        new TWEEN.Tween(child.position)
                            .to(
                                {
                                    x: targetPosition.x,
                                    y: targetPosition.y,
                                    z: targetPosition.z,
                                },
                                2000
                            )
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .onUpdate(() => { })
                            .start();
                    }
                });
            }

            function initGlobalCenter() {
                const globalBox = new THREE.Box3();
                itemList.forEach(item => globalBox.expandByObject(item._e));
                globalBox.getCenter(globalCenter);
            }

            //合并传入模型
            function mergrModel(obj) {
                // $('#recoverBtn').trigger('click');
                isSimulating = false;
                if (!obj) return;
                obj.traverse((child) => {
                    if (child.isMesh) {
                        // child.material.color = new THREE.Color(0x34afd2);
                        new TWEEN.Tween(child.position)
                            .to(child.userData.originalPosition, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .start();
                    }
                });
            }


            function dzmn(obj) {
                // 子组件位置还原
                obj.traverse((child) => {
                    if (child.isMesh) {
                        // child.material.color = new THREE.Color(0x34afd2);
                        new TWEEN.Tween(child.position)
                            .to(child.userData.originalPosition, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .start();
                    }
                });
                explodeModel(modul, 2000, 1)
                // 获取 stepContent div
                const stepContent = document.getElementById('stepContent');
                stepContent.scrollTo({ left: 0 }); // 强制回到初始位置
                isSimulating = true; // 开始搭载模拟，禁止点击
                let currentTween = null;

                // 使用carryAll数组的顺序来排序
                const carryAllOrderMap = {};
                carryAll.forEach((item, index) => {
                    carryAllOrderMap[item.peName] = index;
                });

                $("#blockDetail").hide().empty();
                // 按照carryAll数组的顺序进行排序
                itemList.sort((a, b) => {
                    const orderA = carryAllOrderMap[a.name];
                    const orderB = carryAllOrderMap[b.name];

                    // 如果在carryAll中找不到对应的模块，排到最后
                    if (orderA === undefined && orderB === undefined) return 0;
                    if (orderA === undefined) return 1;
                    if (orderB === undefined) return -1;

                    return orderA - orderB;
                });
                // ========== 视角控制参数 ==========
                const VIEW_CONFIG = {
                    BASE_DISTANCE_MULTIPLIER: 10,      // 基础距离系数
                    VERTICAL_OFFSET_RATIO: 0.3,         // 垂直偏移比例
                    HORIZONTAL_ANGLE_RANGE: 30,         // 水平观察角度（调整为90度侧视）
                    MIN_DISTANCE: 18.0,                 // 最小观察距离
                    MAX_DISTANCE: 50.0,                 // 最大观察距离
                    TARGET_OFFSET_RATIO: 1,           // 目标点横向偏移比例
                    VERTICAL_ANGLE: 70,                 // 垂直俯视角度
                    SIDE_OFFSET_RATIO: 0.8              // 新增侧向偏移系数
                };

                // ========== 初始化全局中心 ==========
                const globalBox = new THREE.Box3();
                const globalCenter = new THREE.Vector3();
                itemList.forEach(item => globalBox.expandByObject(item._e));
                globalBox.getCenter(globalCenter);
                //初始进度条
                let begin = 0;
                itemList.forEach((item, i) => {
                    // 保持模型位置固定
                    const targetPos = new THREE.Vector3(
                        item.itemX,
                        item.itemY,
                        item.itemZ
                    );

                    // ========== 包围盒计算 ==========
                    const localBox = new THREE.Box3().setFromObject(item._e);
                    const localCenter = new THREE.Vector3();
                    localBox.getCenter(localCenter);
                    const size = new THREE.Vector3();
                    localBox.getSize(size);

                    // ========== 动态视角方向计算 ==========
                    const positionDelta = localCenter.clone().sub(globalCenter);
                    const horizontalAngle = positionDelta.x > 0 ?
                        -VIEW_CONFIG.HORIZONTAL_ANGLE_RANGE :
                        VIEW_CONFIG.HORIZONTAL_ANGLE_RANGE;

                    // ========== 观察距离计算 ==========
                    const modelDiagonal = size.length();
                    let optimalDistance = modelDiagonal * VIEW_CONFIG.BASE_DISTANCE_MULTIPLIER;
                    optimalDistance = THREE.MathUtils.clamp(
                        optimalDistance,
                        VIEW_CONFIG.MIN_DISTANCE,
                        VIEW_CONFIG.MAX_DISTANCE
                    );

                    // ========== 创建动画序列 ==========
                    const tween = new TWEEN.Tween(item._e.position)
                        .to(targetPos, 2000)
                        .easing(TWEEN.Easing.Quadratic.InOut);

                    if (currentTween) {
                        currentTween.chain(tween);
                    } else {
                        tween.start().delay(1000);
                        $('#tipsBox').show();
                    }
                    currentTween = tween;

                    tween.onStart(() => {
                        $('.carryDate[data-carrydate="' + item.carryDate + '"]').parent().find('.click').show();
                        //同级隐藏
                        $('.carryDate[data-carrydate="' + item.carryDate + '"]').parent().find('.click').siblings('img').not(this).hide()
                        //其他的非点击的显示
                        $('.carryDate[data-carrydate="' + item.carryDate + '"]').parent().siblings('div').not(this).find(".click").hide();
                        $('.carryDate[data-carrydate="' + item.carryDate + '"]').parent().siblings('div').not(this).find(".noClick").show();

                        // 计算需要滚动的距离
                        if (stepData.indexOf(item.carryDate) != -1 && begin != stepData.indexOf(item.carryDate) && stepData.indexOf(item.carryDate) > 0) {
                            //平滑滚动到目标位置
                            const scrollDistance = 214
                            stepContent.scrollTo({
                                left: stepContent.scrollLeft + scrollDistance,
                                behavior: 'smooth' // 启用平滑滚动
                            });
                            begin = stepData.indexOf(item.carryDate);
                        }


                        // ========== 模型高亮 ==========
                        animateColorChange(item._e, new THREE.Color(0xFF722B));

                        // ========== 目标观察点计算 ==========
                        const sideOffset = size.x * VIEW_CONFIG.SIDE_OFFSET_RATIO *
                            (horizontalAngle > 0 ? 1 : -1);
                        const targetLookAt = localCenter.clone()
                            .add(new THREE.Vector3(
                                sideOffset,
                                0,
                                size.z * VIEW_CONFIG.TARGET_OFFSET_RATIO
                            ));

                        // ========== 相机位置计算 ==========
                        const horizontalRadian = THREE.MathUtils.degToRad(horizontalAngle);
                        const verticalRadian = THREE.MathUtils.degToRad(VIEW_CONFIG.VERTICAL_ANGLE);

                        // 球坐标计算基础位置
                        const cameraPosition = new THREE.Vector3()
                            .setFromSphericalCoords(
                                optimalDistance,
                                verticalRadian,
                                horizontalRadian
                            )
                            .add(targetLookAt);

                        // 横向位置补偿
                        cameraPosition.x += size.x * VIEW_CONFIG.TARGET_OFFSET_RATIO *
                            (horizontalAngle > 0 ? 1 : -1);
                        // 垂直偏移
                        cameraPosition.y += size.y * VIEW_CONFIG.VERTICAL_OFFSET_RATIO;

                        // ========== 相机动画 ==========
                        // new TWEEN.Tween(camera.position)
                        //     .to(cameraPosition, 1800)
                        //     .easing(TWEEN.Easing.Quadratic.InOut)
                        //     .start();

                        // ========== 信息提示 ==========
                        requestAnimationFrame(() => {
                            const $typed = $(`<span id='typed${i}' class="typed"></span>`)
                                .css({ opacity: 0 })
                                .animate({ opacity: 1 }, 500);
                            $('#tipsBox').empty().append($typed);

                            new Typed(`#typed${i}`, {
                                strings: [
                                    `<span class="carryInfo">总段名称:</span><span class="carryInfo">${item.name}</span><br>
                                         <span class="carryInfo">搭载日期:</span><span class="carryInfo">${item.carryDate}</span>`
                                ],
                                typeSpeed: 10,
                                startDelay: 100,
                                showCursor: false,
                                loop: false
                            });
                        });
                    });

                    tween.onComplete(() => {
                        // ========== 恢复模型颜色 ==========
                        item._e.material.color = new THREE.Color(0x34afd2);
                        item._e.material.opacity = 0.6;
                        // ========== 清理提示信息 ==========
                        $(`#typed${i}`).fadeOut(500, function () {
                            $(this).remove();
                        });

                        // 如果是最后一个模块，恢复点击功能
                        if (i === itemList.length - 1) {
                            isSimulating = false; // 模拟结束，恢复点击
                            $('#recoverBtn').trigger('click');
                            $('.nodeItem').find(".click").hide();
                            $('.nodeItem').find(".noClick").show();
                        }
                    });
                });
            }


            // 辅助函数：恢复全局状态
            // function restoreGlobalState() {
            //     isSimulating = false;
            //     $('#recoverBtn').trigger('click'); // 恢复按钮功能
            //     // 清理所有 TWEEN 动画
            //     TWEEN.removeAll();
            //     // 其他全局清理...
            // }

            function hidemodel(obj) {
                obj.children.forEach(function (item) {
                    new TWEEN.Tween(item.position)
                        .to({ x: 1000, y: 1000, z: 1000 }, 2000)
                        .easing(TWEEN.Easing.Quadratic.InOut)
                        .start();
                })

            }

            var highlightedModel = null;
            // 全局变量存储当前线条和看板引用
            var currentLine = null;
            var currentInfoPanel = null;
            function del(name) {
                if (name == '') {
                    return;
                }
                // ========== 清理旧线条和看板 ==========

                let newCarryDetail = carryAll.filter(item => item.peName == name);
                let peName = '';
                let carryDate = '';
                let blockNos = '';
                let l = '', h = '', w = '';
                if (newCarryDetail.length > 0) {
                    carryDate = newCarryDetail[0].carryDate || '';
                    peName = newCarryDetail[0].peName;
                    blockNos = newCarryDetail[0].blockNoList.length > 0 ? newCarryDetail[0].blockNoList.join(",") : "";
                    l = newCarryDetail[0].length || '';
                    h = newCarryDetail[0].height || '';
                    w = newCarryDetail[0].width || '';
                }

                if (currentLine) {
                    scene.remove(currentLine);
                    currentLine.geometry.dispose();
                    currentLine.material.dispose();
                    currentLine = null;
                }
                if (currentInfoPanel) {
                    // currentInfoPanel.classList.remove('visible');
                    // setTimeout(() => currentInfoPanel.remove(), 300);
                    currentInfoPanel.remove(); // 直接移除DOM元素
                    currentInfoPanel = null;
                }
                // ========== 生成新连接线 ==========
                const lineGeometry = new THREE.BufferGeometry();
                const points = [];
                const nameNodeLine = scene.getObjectByName(name);
                // 获取模型中心点（起点）
                const modelCenter = getCenterPoint(nameNodeLine);

                // 计算看板位置（终点：模型右侧偏移）
                const panelPosition = modelCenter.clone().add(new THREE.Vector3(5, 2, 0));
                points.push(modelCenter);
                points.push(panelPosition);
                lineGeometry.setFromPoints(points);

                const lineMaterial = new THREE.LineBasicMaterial({
                    color: 0xFFFFFF,
                    linewidth: 2
                });
                currentLine = new THREE.Line(lineGeometry, lineMaterial);
                scene.add(currentLine);

                // ========== 生成信息看板 ==========
                const infoPanel = document.createElement('div');
                infoPanel.className = 'info-panel';
                infoPanel.innerHTML = `
<!--                <div class="monitor-frame"> &lt;!&ndash; 外框 &ndash;&gt;-->
    <div class="screen-content"> <!-- 屏幕内容区域 -->
      <div class="header-line"> <!-- 标题行 -->
        <div class="close-btn" onclick="this.closest('.info-panel').remove()">×</div>
        <div class="title-line">搭载日：${carryDate}</div>
      </div>
      <div class="info-grid"> <!-- 信息表格布局 -->
        <div class="sub-line"><span class="lableName">总段：</span><span class="text-content">${peName}</span></div>
        <div class="size-line"><span class="lableName">分段：</span><span class="text-content">${blockNos}</span></div>
        <div class="size-line"><span class="lableName">长宽高：</span><span class="text-content" style="letter-spacing: -3px">${l} × ${w} × ${h}</span></div>
      </div>
    </div>
<!--  </div>-->
                     `;
                document.body.appendChild(infoPanel);
                // 显示动画
                setTimeout(() => infoPanel.classList.add('visible'), 10);
                currentInfoPanel = infoPanel;
                // ========== 看板位置同步更新（随模型移动） ==========
                const updatePanelPosition = () => {
                    if (!currentInfoPanel) return;

                    // 将 3D 坐标转换为屏幕坐标
                    const vector = panelPosition.clone().project(camera);
                    const x = (vector.x * 0.5 + 0.5) * window.innerWidth;
                    const y = (vector.y * -0.5 + 0.5) * window.innerHeight;

                    currentInfoPanel.style.left = `${x + 20}px`; // 偏移 20px 避免遮挡
                    currentInfoPanel.style.top = `${y}px`;
                };

                // 在动画循环中更新位置
                const animate = () => {
                    if (currentInfoPanel) {
                        updatePanelPosition();
                        requestAnimationFrame(animate);
                    }
                };
                animate();

                // ========== 关闭按钮事件 ==========
                infoPanel.querySelector('.close-btn').addEventListener('click', () => {
                    scene.remove(currentLine);
                    currentLine.geometry.dispose();
                    currentLine.material.dispose();
                    currentLine = null;
                    infoPanel.remove();
                    currentInfoPanel = null;
                });




                // 重置旧高亮模型的颜色
                if (highlightedModel) {
                    highlightedModel.material.color = new THREE.Color(0xffffff); // 默认颜色
                }
                const nameNode = scene.getObjectByName(name);
                nameNode.material.color = new THREE.Color(0xFF722B);
                highlightedModel = nameNode;
                if (oldintersects.length > 30) {
                    oldintersects = [];
                }
                let targetPositionOld;
                oldintersects.push(nameNode);
                if (oldintersects.length > 1) {
                    if (oldintersects[oldintersects.length - 2].material.color.b == 1) {
                        oldintersects[oldintersects.length - 2].material.color = new THREE.Color(0x34afd2);
                    }
                    targetPositionOld = getCenterPoint(oldintersects[oldintersects.length - 2]);
                }

                const obj = nameNode;

                $("#selectColor").change(function (e) {
                    const color = $("#selectColor").val();
                    nameNode.material.color = new THREE.Color(color);
                });
                $("#changeShow").change(function (e) {
                    const type = $("#changeShow").val();
                    if (type == 0) {
                        nameNode.visible = true;
                    } else {
                        nameNode.visible = false;
                    }
                });

                var box = new THREE.Box3();
                box.expandByObject(nameNode);
                const center = getCenterPoint(obj);

                // 计算目标相机位置
                const size = box.getSize(new THREE.Vector3());
                const maxDimension = Math.max(size.x, size.y, size.z);
                const fov = camera.fov * (Math.PI / 180);
                const distance = (maxDimension / 2) / Math.tan(fov / 2) * 3; // 增加3倍安全边距

                // 获取当前相机方向
                var direction = new THREE.Vector3();
                camera.getWorldDirection(direction);
                direction.negate();

                // 计算目标相机位置
                const targetCameraPosition = center.clone().add(direction.multiplyScalar(distance));

                // 计算目标控制器目标点
                const targetControlsTarget = center.clone();

                // 创建相机位置动画
                new TWEEN.Tween(camera.position)
                    .to(targetCameraPosition, 1000)
                    .easing(TWEEN.Easing.Quadratic.InOut)
                    .start();

                // 创建控制器目标点动画
                new TWEEN.Tween(controls.target)
                    .to(targetControlsTarget, 1000)
                    .easing(TWEEN.Easing.Quadratic.InOut)
                    .onUpdate(() => {
                        controls.update();
                    })
                    .start();

                // ========== 信息提示 ==========


            }

            init()
            $("#search").on('click', function (e) {
                const name = $("#nameOption").val()
                del(name)
            })



            // ========== 1. 全局状态管理 ==========
            let isTransitioning = false; // 切换锁定状态
            let currentColorTween = null; // 颜色动画控制

            // ========== 2. 修改颜色切换逻辑 ==========
            function animateColorChange(mesh, targetColor) {
                if (currentColorTween) {
                    currentColorTween.stop();
                    TWEEN.remove(currentColorTween);
                }

                currentColorTween = new TWEEN.Tween(mesh.material.color)
                    .to(targetColor, 500) // 500ms颜色渐变
                    .onUpdate(() => {
                        mesh.material.needsUpdate = true; // 强制材质更新
                    })
                    .start();
            }

            // ========== 手动排序功能 ==========
            function enterManualSortMode() {
                if (!modul || itemList.length === 0) {
                    layer.msg('请先加载模型数据');
                    return;
                }

                // 如果正在搭载模拟，先停止
                if (isSimulating) {
                    layer.msg('请先停止搭载模拟');
                    return;
                }

                isManualSortMode = true;
                sortOrder = [];
                sortModules = [];
                originalModulePositions.clear();

                // 停止所有动画
                TWEEN.removeAll();

                // 禁用缩放功能，保持默认大小
                controls.enableZoom = false;

                // 重置相机到默认位置和缩放
                camera.position.set(0, 10, 50);
                controls.target.set(0, 0, 0);
                controls.update();

                // 显示排序面板
                $('#sortOrderDisplay').addClass('active');

                // 保存原始位置并创建线框模型
                createFrameModel();

                // 将模块分散到画布四周
                disperseModules();

                // 更新排序显示
                updateSortOrderDisplay();

                layer.msg('手动排序模式已开启，点击模块进行搭载');
            }

            function createFrameModel() {
                // 创建只显示线框的模型副本
                if (frameModel) {
                    scene.remove(frameModel);
                }

                frameModel = modul.clone();
                frameModel.traverse((child) => {
                    if (child.isMesh) {
                        // 只保留线框，移除填充材质
                        child.material = new THREE.MeshBasicMaterial({
                            color: 0xffffff,
                            transparent: true,
                            opacity: 0.1,
                            wireframe: true
                        });
                    }
                });

                scene.add(frameModel);
            }

            function disperseModules() {
                const canvas = document.getElementById('canvas');

                itemList.forEach((item, index) => {
                    // 保存原始位置和缩放
                    originalModulePositions.set(item.name, {
                        x: item._e.position.x,
                        y: item._e.position.y,
                        z: item._e.position.z,
                        scaleX: item._e.scale.x,
                        scaleY: item._e.scale.y,
                        scaleZ: item._e.scale.z
                    });

                    // 计算3D空间中的分散位置（围绕模型四周）
                    const totalModules = itemList.length;
                    const baseRadius = 80; // 基础分散半径
                    const moduleSpacing = 8; // 模块之间的最小间距

                    // 动态计算每条边的模块数量，确保不重叠
                    const maxModulesPerSide = Math.ceil(totalModules / 4);
                    const actualModulesPerSide = Math.min(maxModulesPerSide, 15); // 限制每边最多15个

                    let sideIndex = Math.floor(index / actualModulesPerSide);
                    let positionInSide = index % actualModulesPerSide;

                    // 如果模块太多，分配到多层
                    if (index >= actualModulesPerSide * 4) {
                        const extraIndex = index - actualModulesPerSide * 4;
                        sideIndex = Math.floor(extraIndex / actualModulesPerSide);
                        positionInSide = extraIndex % actualModulesPerSide;
                    }

                    let disperseX, disperseY, disperseZ;
                    const layerOffset = Math.floor(index / (actualModulesPerSide * 4)) * 15; // 多层时的偏移

                    switch (sideIndex % 4) {
                        case 0: // 前方（Z轴正方向）
                            disperseX = (positionInSide - (actualModulesPerSide - 1) / 2) * moduleSpacing;
                            disperseY = 0;
                            disperseZ = baseRadius + layerOffset;
                            break;
                        case 1: // 右侧（X轴正方向）
                            disperseX = baseRadius + layerOffset;
                            disperseY = (positionInSide - (actualModulesPerSide - 1) / 2) * moduleSpacing;
                            disperseZ = 0;
                            break;
                        case 2: // 后方（Z轴负方向）
                            disperseX = ((actualModulesPerSide - 1) / 2 - positionInSide) * moduleSpacing;
                            disperseY = 0;
                            disperseZ = -(baseRadius + layerOffset);
                            break;
                        case 3: // 左侧（X轴负方向）
                        default:
                            disperseX = -(baseRadius + layerOffset);
                            disperseY = ((actualModulesPerSide - 1) / 2 - positionInSide) * moduleSpacing;
                            disperseZ = 0;
                            break;
                    }

                    // 移动模块到分散位置并缩小
                    new TWEEN.Tween(item._e.position)
                        .to({ x: disperseX, y: disperseY, z: disperseZ }, 2000)
                        .easing(TWEEN.Easing.Quadratic.InOut)
                        .start();

                    // 缩小模块
                    new TWEEN.Tween(item._e.scale)
                        .to({ x: 0.3, y: 0.3, z: 0.3 }, 2000)
                        .easing(TWEEN.Easing.Quadratic.InOut)
                        .start();

                    // 创建可点击的排序模块UI，使用动态位置更新
                    createSortModuleUI(item, index);
                });
            }

            function createSortModuleUI(item, index) {
                const canvas = document.getElementById('canvas');
                const moduleDiv = document.createElement('div');
                moduleDiv.className = 'sort-module';
                moduleDiv.dataset.moduleName = item.name;

                moduleDiv.innerHTML = `
                    <div class="sort-module-name">${item.name}</div>
                    <div class="sort-module-date">${item.carryDate || '未设定'}</div>
                `;

                // 初始位置设置
                updateModuleUIPosition(moduleDiv, item);

                // 点击事件
                moduleDiv.addEventListener('click', (e) => {
                    e.stopPropagation();
                    if (isManualSortMode) {
                        assembleModule(item);
                        moduleDiv.remove();
                        // 从sortModules数组中移除
                        const index = sortModules.indexOf(moduleDiv);
                        if (index > -1) {
                            sortModules.splice(index, 1);
                        }
                    }
                });

                // 鼠标悬停事件 - 高亮对应的3D模块
                moduleDiv.addEventListener('mouseenter', (e) => {
                    if (isManualSortMode) {
                        highlightFrameModule(item.name, true);
                    }
                });

                moduleDiv.addEventListener('mouseleave', (e) => {
                    if (isManualSortMode) {
                        highlightFrameModule(item.name, false);
                    }
                });

                canvas.appendChild(moduleDiv);
                sortModules.push(moduleDiv);

                // 设置初始位置（固定位置，不再动态更新）
                updateModuleUIPosition(moduleDiv, item);
            }





            function updateModuleUIPosition(moduleDiv, item) {
                if (!moduleDiv.parentNode || !isManualSortMode) {
                    return;
                }

                const canvas = document.getElementById('canvas');

                // 获取模块在sortModules数组中的索引
                const moduleIndex = sortModules.indexOf(moduleDiv);

                // 固定排列参数（根据用户调整的尺寸）
                const cardWidth = 74;  // 用户调整后的宽度
                const cardHeight = 47; // 用户调整后的高度
                const maxHorizontal = 18; // 横向18个
                const maxVertical = 14;   // 纵向14个
                const horizontalCornerOffset = 80; // 增加横向角落避让距离

                // 计算可用空间
                const availableWidth = canvas.offsetWidth - 2 * horizontalCornerOffset;
                const availableHeight = canvas.offsetHeight; // 纵向不保留安全空间

                // 计算间距
                const horizontalSpacing = Math.max(2, (availableWidth - maxHorizontal * cardWidth) / (maxHorizontal + 1));
                const verticalSpacing = (availableHeight - maxVertical * cardHeight) / (maxVertical + 1);

                let finalX, finalY;

                // 按顺序分配到四个边缘
                if (moduleIndex < maxHorizontal) {
                    // 顶部边缘 - 从左到右，横向保留角落避让
                    finalX = horizontalCornerOffset + horizontalSpacing + moduleIndex * (cardWidth + horizontalSpacing);
                    finalY = 0; // 纵向贴顶，不保留安全空间
                } else if (moduleIndex < maxHorizontal + maxVertical) {
                    // 右侧边缘 - 从上到下，纵向不保留安全空间
                    const rightIndex = moduleIndex - maxHorizontal;
                    finalX = canvas.offsetWidth - cardWidth;
                    finalY = verticalSpacing + rightIndex * (cardHeight + verticalSpacing);
                } else if (moduleIndex < maxHorizontal * 2 + maxVertical) {
                    // 底部边缘 - 从右到左，横向保留角落避让
                    const bottomIndex = moduleIndex - maxHorizontal - maxVertical;
                    finalX = canvas.offsetWidth - horizontalCornerOffset - horizontalSpacing - cardWidth - bottomIndex * (cardWidth + horizontalSpacing);
                    finalY = canvas.offsetHeight - cardHeight; // 纵向贴底，不保留安全空间
                } else if (moduleIndex < maxHorizontal * 2 + maxVertical * 2) {
                    // 左侧边缘 - 从下到上，纵向不保留安全空间
                    const leftIndex = moduleIndex - maxHorizontal * 2 - maxVertical;
                    finalX = 0;
                    finalY = canvas.offsetHeight - verticalSpacing - cardHeight - leftIndex * (cardHeight + verticalSpacing);
                } else {
                    // 超过64个模块时，在底部边缘上方生成新行
                    const totalPerimeter = maxHorizontal * 2 + maxVertical * 2; // 64个
                    const extraIndex = moduleIndex - totalPerimeter;
                    const rowSpacing = 15; // 行间距15px

                    // 计算在第几行（从底部向上）
                    const currentRow = Math.floor(extraIndex / maxHorizontal);
                    const positionInRow = extraIndex % maxHorizontal;

                    // 在底部边缘上方生成新行，避免重叠
                    finalX = horizontalCornerOffset + horizontalSpacing + positionInRow * (cardWidth + horizontalSpacing);
                    finalY = canvas.offsetHeight - cardHeight - (currentRow + 1) * (cardHeight + rowSpacing);
                }

                // 确保在画布范围内
                finalX = Math.max(0, Math.min(finalX, canvas.offsetWidth - cardWidth));
                finalY = Math.max(0, Math.min(finalY, canvas.offsetHeight - cardHeight));

                moduleDiv.style.left = finalX + 'px';
                moduleDiv.style.top = finalY + 'px';
                moduleDiv.style.opacity = '1';
            }





            function highlightFrameModule(moduleName, isHighlight) {
                if (!frameModel) return;

                frameModel.traverse((child) => {
                    if (child.isMesh && child.name === moduleName) {
                        // 如果模块已经被搭载，不进行高亮
                        if (child.userData.isAssembled) {
                            return;
                        }

                        if (isHighlight) {
                            // 保存原始状态（只在第一次高亮时保存）
                            if (!child.userData.originalMaterial) {
                                child.userData.originalMaterial = child.material;
                                // 保存原始缩放
                                child.userData.originalScale = {
                                    x: child.scale.x,
                                    y: child.scale.y,
                                    z: child.scale.z
                                };
                            }

                            // 方案1: 创建一个更明显的高亮材质
                            const highlightMaterial = new THREE.MeshBasicMaterial({
                                color: 0xff4400,        // 更鲜艳的橙红色
                                transparent: true,
                                opacity: 0.8,
                                wireframe: true,
                                wireframeLinewidth: 5   // 更粗的线条
                            });

                            // 应用高亮材质
                            child.material = highlightMaterial;

                            // 方案2: 添加发光效果
                            // child.material.emissive.setHex(0xff2200);

                            // 方案3: 增加模型尺寸，使其更突出（基于原始尺寸）
                            child.scale.set(
                                child.userData.originalScale.x * 1.05,
                                child.userData.originalScale.y * 1.05,
                                child.userData.originalScale.z * 1.05
                            );

                            // 方案4: 添加闪烁效果
                            child.userData.isBlinking = true;
                            startBlinkingEffect(child);

                        } else {
                            // 恢复原始状态
                            if (child.userData.originalMaterial) {
                                child.material = child.userData.originalMaterial;
                            }

                            // 恢复原始尺寸（使用保存的原始缩放值）
                            if (child.userData.originalScale) {
                                child.scale.set(
                                    child.userData.originalScale.x,
                                    child.userData.originalScale.y,
                                    child.userData.originalScale.z
                                );
                            }

                            // 停止闪烁效果
                            child.userData.isBlinking = false;
                        }
                        child.material.needsUpdate = true;
                    }
                });
            }

            // 闪烁效果函数
            function startBlinkingEffect(mesh) {
                if (!mesh.userData.isBlinking) return;

                const originalOpacity = mesh.material.opacity;

                // 创建闪烁动画
                new TWEEN.Tween({ opacity: originalOpacity })
                    .to({ opacity: 0.3 }, 300)
                    .easing(TWEEN.Easing.Quadratic.InOut)
                    .onUpdate(function (obj) {
                        if (mesh.material) {
                            mesh.material.opacity = obj.opacity;
                        }
                    })
                    .yoyo(true)
                    .repeat(Infinity)
                    .start();
            }

            function assembleModule(item) {
                // 添加到排序数组
                sortOrder.push({
                    name: item.name,
                    carryDate: item.carryDate,
                    peId: item.peId,
                    timestamp: Date.now()
                });

                // 获取原始位置和缩放
                const originalData = originalModulePositions.get(item.name);
                if (originalData) {
                    // 动画移动到原始位置
                    new TWEEN.Tween(item._e.position)
                        .to({ x: originalData.x, y: originalData.y, z: originalData.z }, 1500)
                        .easing(TWEEN.Easing.Quadratic.InOut)
                        .onStart(() => {
                            // 高亮显示
                            animateColorChange(item._e, new THREE.Color(0xFF722B));
                        })
                        .onComplete(() => {
                            // 恢复颜色
                            item._e.material.color = new THREE.Color(0x34afd2);
                        })
                        .start();

                    // 动画恢复原始缩放
                    new TWEEN.Tween(item._e.scale)
                        .to({ x: originalData.scaleX, y: originalData.scaleY, z: originalData.scaleZ }, 1500)
                        .easing(TWEEN.Easing.Quadratic.InOut)
                        .start();
                }

                // 在线框模型中标记该模块为已搭载状态
                markModuleAsAssembled(item.name);

                // 更新排序显示
                updateSortOrderDisplay();

                // 检查是否完成所有搭载
                if (sortOrder.length === itemList.length) {
                    sortOrder.forEach((el) => {
                        el.shipId = shipId
                        carryAll.forEach((item) => {
                            if (el.name == item.peName) {
                                el.peId = item.peId
                            }
                        })
                    })
                    setTimeout(() => {
                        layer.msg('所有模块搭载完成！正在退出排序模式...');
                        console.log('手动排序完成，搭载顺序:', sortOrder);
                        febs.postArray(ctx + 'visualPeSort/saveList', sortOrder, function () {


                        })

                        // 自动退出排序模式
                        setTimeout(() => {
                            exitManualSortMode();
                        }, 1000);
                    }, 1500);
                }
            }

            function markModuleAsAssembled(moduleName) {
                if (!frameModel) return;

                frameModel.traverse((child) => {
                    if (child.isMesh && child.name === moduleName) {
                        // 停止任何高亮效果
                        child.userData.isBlinking = false;

                        // 恢复原始尺寸
                        if (child.userData.originalScale) {
                            child.scale.set(
                                child.userData.originalScale.x,
                                child.userData.originalScale.y,
                                child.userData.originalScale.z
                            );
                        }

                        // 设置为已搭载状态 - 使用白色线框
                        child.material = new THREE.MeshBasicMaterial({
                            color: 0xffffff,        // 白色线框
                            transparent: true,
                            opacity: 0.6,           // 适中透明度，表示已完成
                            wireframe: true,
                            wireframeLinewidth: 1
                        });

                        // 标记为已搭载，防止再次高亮
                        child.userData.isAssembled = true;

                        child.material.needsUpdate = true;
                    }
                });
            }

            function updateSortOrderDisplay() {
                const listElement = document.getElementById('sortOrderList');
                listElement.innerHTML = '';

                sortOrder.forEach((item, index) => {
                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'sort-order-item';
                    itemDiv.draggable = true;
                    itemDiv.dataset.index = index;
                    itemDiv.dataset.moduleName = item.name; // 保存模块名称用于高亮

                    itemDiv.innerHTML = `
                        <div class="item-content">
                            ${index + 1}. ${item.name} (${item.carryDate || '未设定'})
                        </div>
                    `;

                    // 添加拖拽事件监听器
                    addDragListeners(itemDiv);

                    // 添加鼠标悬停高亮事件
                    addHoverHighlightListeners(itemDiv);

                    listElement.appendChild(itemDiv);
                });
            }

            function addDragListeners(itemDiv) {
                itemDiv.addEventListener('dragstart', (e) => {
                    itemDiv.classList.add('dragging');
                    e.dataTransfer.effectAllowed = 'move';
                    e.dataTransfer.setData('text/html', itemDiv.outerHTML);
                    e.dataTransfer.setData('text/plain', itemDiv.dataset.index);
                });

                itemDiv.addEventListener('dragend', (e) => {
                    itemDiv.classList.remove('dragging');
                });

                itemDiv.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';

                    const draggingItem = document.querySelector('.dragging');
                    if (draggingItem !== itemDiv) {
                        itemDiv.classList.add('drag-over');
                    }
                });

                itemDiv.addEventListener('dragleave', (e) => {
                    itemDiv.classList.remove('drag-over');
                });

                itemDiv.addEventListener('drop', (e) => {
                    e.preventDefault();
                    itemDiv.classList.remove('drag-over');

                    const draggedIndex = parseInt(e.dataTransfer.getData('text/plain'));
                    const targetIndex = parseInt(itemDiv.dataset.index);

                    if (draggedIndex !== targetIndex) {
                        // 重新排列sortOrder数组
                        const draggedItem = sortOrder.splice(draggedIndex, 1)[0];
                        sortOrder.splice(targetIndex, 0, draggedItem);

                        // 更新显示
                        updateSortOrderDisplay();

                        layer.msg('排序已更新');
                    }
                });
            }

            function addHoverHighlightListeners(itemDiv) {
                itemDiv.addEventListener('mouseenter', (e) => {
                    if (isManualSortMode) {
                        const moduleName = itemDiv.dataset.moduleName;
                        highlightAssembledModule(moduleName, true);
                    }
                });

                itemDiv.addEventListener('mouseleave', (e) => {
                    if (isManualSortMode) {
                        const moduleName = itemDiv.dataset.moduleName;
                        highlightAssembledModule(moduleName, false);
                    }
                });
            }

            function highlightAssembledModule(moduleName, isHighlight) {
                // 高亮已搭载完成的实体模型块，而不是线框模型
                if (!modul) return;

                modul.traverse((child) => {
                    if (child.isMesh && child.name === moduleName) {
                        if (isHighlight) {
                            // 保存原始状态（如果还没保存的话）
                            if (!child.userData.originalStateForHover) {
                                child.userData.originalStateForHover = {
                                    color: child.material.color.clone(),
                                    emissive: child.material.emissive.clone(),
                                    opacity: child.material.opacity,
                                    scaleX: child.scale.x,
                                    scaleY: child.scale.y,
                                    scaleZ: child.scale.z
                                };
                            }

                            // 高亮显示已搭载的实体模块 - 使用橙色
                            child.material.color.setHex(0xff4400);        // 橙色高亮
                            child.material.emissive.setHex(0xff2200);     // 橙红色发光效果
                            child.material.opacity = 1.0;                // 完全不透明

                            // 基于原始尺寸放大
                            const originalState = child.userData.originalStateForHover;
                            child.scale.set(
                                originalState.scaleX * 1.05,
                                originalState.scaleY * 1.05,
                                originalState.scaleZ * 1.05
                            );

                        } else {
                            // 恢复原始状态
                            if (child.userData.originalStateForHover) {
                                const originalState = child.userData.originalStateForHover;
                                child.material.color.copy(originalState.color);
                                child.material.emissive.copy(originalState.emissive);
                                child.material.opacity = originalState.opacity;

                                // 恢复原始尺寸
                                child.scale.set(
                                    originalState.scaleX,
                                    originalState.scaleY,
                                    originalState.scaleZ
                                );
                            } else {
                                // 如果没有保存的原始状态，恢复到默认状态
                                child.material.color.setHex(0x34afd2);   // 默认蓝色
                                child.material.emissive.setHex(0x000000); // 无发光
                                child.material.opacity = 1.0;
                                // 不修改缩放，保持当前状态
                            }
                        }
                        child.material.needsUpdate = true;
                    }
                });
            }

            // 重新获取carryAll数据的函数
            function refreshCarryAllData() {
                if (!currentSelectedDate) {
                    console.warn('没有选中的日期，无法刷新数据');
                    return;
                }

                febs.getSyn(ctx + "dockship/carryDetail", { shipId: shipId, date: currentSelectedDate }, function (data) {
                    // 更新carryAll数据
                    carryAll = data.data.carryAll;
                });
            }

            function exitManualSortMode() {
                isManualSortMode = false;

                // 恢复缩放功能
                controls.enableZoom = true;

                // 隐藏排序面板
                $('#sortOrderDisplay').removeClass('active');

                // 移除排序模块UI
                sortModules.forEach(module => module.remove());
                sortModules = [];

                // 移除线框模型
                if (frameModel) {
                    scene.remove(frameModel);
                    frameModel = null;
                }

                // 恢复所有模块到原始位置和缩放
                itemList.forEach(item => {
                    const originalData = originalModulePositions.get(item.name);
                    if (originalData) {
                        // 恢复位置
                        new TWEEN.Tween(item._e.position)
                            .to({ x: originalData.x, y: originalData.y, z: originalData.z }, 1000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .start();

                        // 恢复缩放
                        new TWEEN.Tween(item._e.scale)
                            .to({ x: originalData.scaleX, y: originalData.scaleY, z: originalData.scaleZ }, 1000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .start();
                    }
                });

                // 清空数据
                originalModulePositions.clear();
                sortOrder = [];

                // 重置按钮文本
                $('#manualSort').text('手动排序');

                // 刷新carryAll数据
                refreshCarryAllData();

                layer.msg('已退出手动排序模式');
            }

            function resetSort() {
                if (!isManualSortMode) return;

                // 清空排序记录
                sortOrder = [];

                // 移除现有UI
                sortModules.forEach(module => module.remove());
                sortModules = [];

                // 重置线框模型中的已搭载标记
                if (frameModel) {
                    frameModel.traverse((child) => {
                        if (child.isMesh && child.userData.isAssembled) {
                            child.userData.isAssembled = false;
                            // 恢复原始材质
                            if (child.userData.originalMaterial) {
                                child.material = child.userData.originalMaterial;
                                child.material.needsUpdate = true;
                            }
                        }
                    });
                }

                // 重新分散模块
                disperseModules();

                // 更新显示
                updateSortOrderDisplay();

                layer.msg('排序已重置');
            }



            $view.find(".nodeItem").on("click", function (e) {

                // 同时执行三个动画,还原
                Promise.all([
                    // 模型整体位置还原（从当前位置到(0,0,-10)）
                    new Promise(resolve => {
                        new TWEEN.Tween(modul.position)
                            .to({ x: 0, y: 0, z: 0 }, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .onComplete(resolve)
                            .start();
                    }),


                    // 相机位置和视角还原
                    new Promise(resolve => {
                        // 相机位置到(0, 10, 50)
                        new TWEEN.Tween(camera.position)
                            .to({ x: 0, y: 10, z: 50 }, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .start();

                        // 控制中心点回到模型中心(0,0,0)
                        new TWEEN.Tween(controls.target)
                            .to({ x: 0, y: 0, z: 0 }, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .onUpdate(() => controls.update())
                            .onComplete(() => {
                                // controls.reset(); // 重置轨道控制器
                                resolve();
                            })
                            .start();
                    })
                ]).then(() => {
                    controls.enabled = true; // 恢复控制器
                    controls.minDistance = 0;
                    controls.update();
                });
                //取消动画
                TWEEN.removeAll();
                //清空数据
                $('#tipsBox').hide().empty();
                $("#blockDetail").hide().empty();
                // $('#recoverBtn').trigger('click');
                let clickDate = $(this).find(".carryDate").attr("data-carrydate");
                currentSelectedDate = clickDate; // 保存当前选中的日期
                $(this).find('.click').show();
                //同级隐藏
                $(this).find('.click').siblings('img').not(this).hide()
                //其他的非点击的显示
                $(this).siblings('div').not(this).find(".click").hide();
                $(this).siblings('div').not(this).find(".noClick").show();
                let newCarryDat = [];
                febs.getSyn(ctx + "dockship/carryDetail", { shipId: shipId, date: clickDate }, function (data) {
                    $.each(data.data.carryDetail, function (i, val) {
                        newCarryDat.push({ peName: val.peName, carryDate: val.carryDate })
                    })
                    initModel(newCarryDat, modelScalar, modelURL)
                })
            })




            $("#splitpy").on('click', function (e) {
                explodeModel(modul, 2000, 1)

            })
            $("#splitbz").on('click', function (e) {
                explodeModel(modul, 2000, 2)
            })
            $("#merge").on('click', function (e) {
                mergrModel(modul)
            })
            $("#dzmn").on('click', function (e) {
                layer.closeAll();
                dzmn(modul)
            })
            $("#hidemodel").on('click', function (e) {
                hidemodel(modul)
            })

            // 手动排序按钮事件
            $("#manualSort").on('click', function (e) {
                if (isManualSortMode) {
                    exitManualSortMode();
                    $(this).text('手动排序');
                } else {
                    enterManualSortMode();
                    $(this).text('退出排序');
                }
            })

            // 排序控制按钮事件
            $("#exitSortMode").on('click', function (e) {
                exitManualSortMode();
            })

            $("#resetSort").on('click', function (e) {
                resetSort();
            })

            // 新的重置排序按钮事件
            $("#resetSortBtn").on('click', function (e) {
                resetSort();
            })


            // 恢复默认排序
            $("#resetSortBtn2").on('click', function (e) {
                febs.getSyn(ctx + "visualPeSort/delete", { shipId: shipId }, function (e) {

                    if (e.code == 200) {
                        layer.msg('重置成功');
                    }

                })
            })

            $('#showUp').click(function () {
                $(this).text() == '旋转开启' ? $(this).text('旋转关闭') : $(this).text('旋转开启')

                controls.autoRotate = !controls.autoRotate
                controls.update();
            })

            // 还原初始状态
            $('#recoverBtn').click(function () {
                $("#showUp").text('旋转关闭');
                $('.close-btn').click()
                controls.autoRotate = false;

                // 如果正在手动排序模式，先退出
                if (isManualSortMode) {
                    exitManualSortMode();
                    layer.msg('已退出手动排序模式并还原模型');
                    return; // 退出排序模式后就不需要继续执行下面的还原逻辑
                }

                isSimulating = false;
                controls.enabled = false; // 禁用控制器
                TWEEN.removeAll();

                // 同时执行三个动画
                Promise.all([
                    // 模型整体位置还原（从当前位置到(0,0,-10)）
                    new Promise(resolve => {
                        new TWEEN.Tween(modul.position)
                            .to({ x: 0, y: 0, z: 0 }, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .onComplete(resolve)
                            .start();
                    }),

                    // 子组件位置还原
                    new Promise(resolve => {
                        mergrModel(modul);
                        setTimeout(resolve, 2000); // 保持与动画同步
                    }),

                    // 相机位置和视角还原
                    new Promise(resolve => {
                        // 相机位置到(0, 10, 50)
                        new TWEEN.Tween(camera.position)
                            .to({ x: 0, y: 10, z: 50 }, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .start();

                        // 控制中心点回到模型中心(0,0,0)
                        new TWEEN.Tween(controls.target)
                            .to({ x: 0, y: 0, z: 0 }, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .onUpdate(() => controls.update())
                            .onComplete(() => {
                                // controls.reset(); // 重置轨道控制器
                                resolve();
                            })
                            .start();
                    })
                ]).then(() => {
                    controls.enabled = true; // 恢复控制器
                    controls.minDistance = 0;
                    controls.update();
                    initModel(carryData, modelScalar, modelURL);
                });

                $('#tipsBox').hide().empty();
                $("#blockDetail").hide().empty();
            });
            $('#modelsChange').click(function () {
                if (chooseModels.length == 0) {
                    changeBox.style.display = 'none'
                    return
                }
                changeBox.innerHTML = 'nigenshan'
                const info = `
                        <p style="margin-top:12px;margin-bottom:15px">数量:
                            <text>${chooseModels.length}<text>
                        <p>
                        <p>颜色:
                        <select id="selectColors" style="width: 100px;height: 30px;margin-bottom:15px">
                            <option value="0xffffff">未设置</option>
                            <option value="rgb(255, 114, 43)">红色</option>
                            <option value="rgb(85, 255, 255)">蓝色</option>
                            <option value="rgb(255, 255, 0)">黄色</option>
                            <option value="rgb(0, 0, 0)">黑色</option>
                        </select>

                        </p>
                        <p>状态:
                        <select id="changeShows" style="width: 100px;height: 30px;margin-bottom:15px">
                            <option value="">请选择</option>
                            <option value="0">显示</option>
                            <option value="1">隐藏</option>
                        </select>
                        </p>
                        `
                changeBox.innerHTML = info;
                changeBox.style.display = 'block'
                changeBox.style.left = '2%'
                changeBox.style.top = '10%'
                $("#selectColors").change(function (e) {
                    const color = $("#selectColors").val()
                    chooseModels.forEach((item) => {
                        item.material.color = new THREE.Color(color);
                    })
                })
                $("#changeShows").change(function (e) {
                    const type = $("#changeShows").val()
                    if (type == 0) {
                        chooseModels.forEach((item) => {
                            item.visible = true
                        })
                    } else {
                        chooseModels.forEach((item) => {
                            item.visible = false
                        })
                    }
                })
            })



        })
    </script>

</body>

</html>